import { VueNodeViewRenderer } from '@tiptap/vue-3';
import Image from '@tiptap/extension-image';
import ImageNodeView from './ImageNodeView.vue';

export default Image.extend({
  addOptions() {
    return {
      ...this.parent?.(),
      HTMLAttributes: {},
      draggable: true,
      resizable: true
    };
  },
  addAttributes() {
    return {
      ...this.parent?.(),
      width: {
        default: null,
        parseHTML: (element) => element.getAttribute('width'),
        renderHTML: (attributes) => {
          if (!attributes.width) return {};
          return { width: attributes.width };
        }
      },
      height: {
        default: null,
        parseHTML: (element) => element.getAttribute('height'),
        renderHTML: (attributes) => {
          if (!attributes.height) return {};
          return { height: attributes.height };
        }
      },
      textAlign: {
        default: null,
        parseHTML: (element) => {
          // 从 margin 样式中解析对齐方式
          const marginLeft = element.style.marginLeft;
          const marginRight = element.style.marginRight;
          const width = element.style.width;

          if (width === '100%') return 'justify';
          if (marginLeft === 'auto' && marginRight === 'auto') return 'center';
          if (marginLeft === 'auto' && marginRight === '0') return 'right';
          if (marginLeft === '0' && marginRight === 'auto') return 'left';

          return null;
        },
        renderHTML: (attributes) => {
          // 不在这里渲染样式，由 Vue 组件处理
          return {};
        }
      }
    };
  },
  addNodeView() {
    return (props) => {
      return VueNodeViewRenderer(ImageNodeView)({
        ...props,
        HTMLAttributes: {
          ...(this.options.HTMLAttributes || {}),
          ...(props.HTMLAttributes || {})
        },
        draggable: this.options.draggable,
        resizable: this.options.resizable
      });
    };
  }
});
