<template>
  <NodeViewWrapper
    as="span"
    class="image-nodeview-wrapper"
    :class="{ selected: selected, resizing: resizing }"
    :style="wrapperStyle"
  >
    <img
      ref="img"
      :src="node.attrs.src"
      :alt="node.attrs.alt"
      :title="node.attrs.title"
      :width="node.attrs.width"
      :height="node.attrs.height"
      class="editor-image"
      :draggable="props.draggable !== false"
      @click="handleClick"
      @mousedown="handleMouseDown"
      @load="handleImageLoad"
    />
    <div
      v-if="selected"
      class="image-resize-handle"
      @mousedown.stop.prevent="startResize"
    ></div>
  </NodeViewWrapper>
</template>

<script setup>
import { ref, onBeforeUnmount, nextTick, computed, watch } from 'vue';
import { NodeViewWrapper } from '@tiptap/vue-3';
const props = defineProps([
  'node',
  'updateAttributes',
  'selected',
  'editor',
  'getPos',
  'draggable'
]);

const img = ref(null);
const resizing = ref(false);
const start = ref({ x: 0, y: 0, width: 0, height: 0 });
const originalWidth = ref(0);
const originalHeight = ref(0);

// 文本对齐相关
const textAlign = ref(props.node.attrs.textAlign || null);
const width = ref(props.node.attrs.width || null);

// 监听节点属性变化
watch(
  () => props.node.attrs.textAlign,
  (newAlign) => {
    textAlign.value = newAlign;
  }
);

watch(
  () => props.node.attrs.width,
  (newWidth) => {
    width.value = newWidth;
  }
);

// 计算外层容器样式，实现 width + margin 对齐
const wrapperStyle = computed(() => {
  const style = {
    display: 'block' // 确保是块级元素，这样 margin 对齐才能生效
  };

  // 如果有明确的宽度，设置宽度
  if (width.value) {
    style.width = width.value + 'px';
  }

  // 使用 margin 实现对齐
  if (textAlign.value) {
    switch (textAlign.value) {
      case 'left':
        style.marginLeft = '0';
        style.marginRight = 'auto';
        break;
      case 'center':
        style.marginLeft = 'auto';
        style.marginRight = 'auto';
        break;
      case 'right':
        style.marginLeft = 'auto';
        style.marginRight = '0';
        break;
      case 'justify':
        style.width = '100%';
        style.marginLeft = '0';
        style.marginRight = '0';
        break;
    }
  }

  return style;
});

function handleImageLoad() {
  if (img.value) {
    originalWidth.value = img.value.naturalWidth;
    originalHeight.value = img.value.naturalHeight;
  }
}

function handleClick(e) {
  // 阻止事件冒泡，避免触发其他选择逻辑
  e.stopPropagation();

  // 如果编辑器存在，设置光标位置到图片节点
  if (props.editor && typeof props.getPos === 'function') {
    const pos = props.getPos();
    if (typeof pos === 'number' && pos >= 0) {
      // 只选择图片节点，不设置文本光标
      props.editor.commands.setNodeSelection(pos);
    }
  }
}

function handleMouseDown(e) {
  // 阻止事件冒泡，避免干扰文本编辑
  e.stopPropagation();
  // 确保编辑器获得焦点
  if (props.editor) {
    props.editor.commands.focus();
  }
}

function startResize(e) {
  if (!img.value) return;

  resizing.value = true;

  // 获取当前图片的实际显示尺寸
  const rect = img.value.getBoundingClientRect();
  const currentWidth = rect.width;
  const currentHeight = rect.height;

  start.value = {
    x: e.clientX,
    y: e.clientY,
    width: currentWidth,
    height: currentHeight
  };

  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);

  // 添加全局样式防止文本选择
  document.body.style.userSelect = 'none';
  document.body.style.cursor = 'se-resize';
}

function handleResize(e) {
  if (!resizing.value) return;

  const dx = e.clientX - start.value.x;
  const dy = e.clientY - start.value.y;

  // 计算新的尺寸，保持宽高比
  const aspectRatio = start.value.width / start.value.height;
  let newWidth = Math.max(50, start.value.width + dx);
  let newHeight = newWidth / aspectRatio;

  // 如果高度变化更大，则以高度为准
  if (Math.abs(dy) > Math.abs(dx)) {
    newHeight = Math.max(50, start.value.height + dy);
    newWidth = newHeight * aspectRatio;
  }

  // 实时更新图片尺寸
  if (img.value) {
    img.value.style.width = `${newWidth}px`;
    img.value.style.height = `${newHeight}px`;
  }
}

function stopResize() {
  if (!resizing.value) return;

  resizing.value = false;

  // 获取最终的尺寸
  if (img.value) {
    const rect = img.value.getBoundingClientRect();
    const finalWidth = Math.round(rect.width);
    const finalHeight = Math.round(rect.height);

    console.log('Final dimensions:', finalWidth, finalHeight);
    console.log('Current node attrs:', props.node.attrs);

    // 更新节点属性
    try {
      props.updateAttributes({
        width: finalWidth,
        height: finalHeight
      });

      // 验证属性是否更新成功
      setTimeout(() => {
        console.log('Updated node attrs:', props.node.attrs);
        if (
          img.value &&
          props.node.attrs.width === finalWidth &&
          props.node.attrs.height === finalHeight
        ) {
          // 只有在属性更新成功后才清除内联样式
          img.value.style.width = '';
          img.value.style.height = '';
          console.log('Inline styles cleared');
        } else {
          console.log('Attributes not updated, keeping inline styles');
        }
      }, 50);
    } catch (error) {
      console.error('Error updating attributes:', error);
    }
  }

  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);

  // 恢复全局样式
  document.body.style.userSelect = '';
  document.body.style.cursor = '';
}

onBeforeUnmount(() => {
  stopResize();
});
</script>

<style lang="less" scoped>
.image-nodeview-wrapper {
  display: block; // 改为 block，支持 margin 对齐
  position: relative;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 100%;
  user-select: none;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }

  &.selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
  }

  &.resizing {
    border-color: #1890ff;
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.3);

    .editor-image {
      opacity: 0.8;
    }

    .image-resize-handle {
      transform: scale(1.2);
      background: linear-gradient(135deg, #40a9ff, #69c0ff);
    }
  }

  .editor-image {
    max-width: 100%;
    height: auto;
    display: block;
    border-radius: 4px;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.95;
    }
  }

  .image-resize-handle {
    position: absolute;
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    border: 2px solid #ffffff;
    border-radius: 50%;
    cursor: se-resize;
    bottom: -8px;
    right: -8px;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: scale(0.95);
    }

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 6px;
      height: 6px;
      background: #ffffff;
      border-radius: 1px;
      transform: translate(-50%, -50%);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-nodeview-wrapper {
    .image-resize-handle {
      width: 20px;
      height: 20px;
      bottom: -10px;
      right: -10px;

      &::before {
        width: 8px;
        height: 8px;
      }
    }
  }
}
</style>
