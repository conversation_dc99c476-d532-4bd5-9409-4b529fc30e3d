import { Node, mergeAttributes } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';
import MermaidNodeView from './MermaidNodeView.vue'; // 路径根据实际情况调整

export default Node.create({
  name: 'mermaid',
  group: 'block',
  atom: true,
  draggable: true,
  selectable: true,
  addAttributes() {
    return {
      code: { default: '' },
      width: { default: 600 },
      height: { default: 100 },
      textAlign: {
        default: null,
        parseHTML: (element) => {
          // 从 margin 样式中解析对齐方式
          const marginLeft = element.style.marginLeft;
          const marginRight = element.style.marginRight;
          const width = element.style.width;

          if (width === '100%') return 'justify';
          if (marginLeft === 'auto' && marginRight === 'auto') return 'center';
          if (marginLeft === 'auto' && marginRight === '0') return 'right';
          if (marginLeft === '0' && marginRight === 'auto') return 'left';

          return null;
        },
        renderHTML: (attributes) => {
          // 不在这里渲染样式，由 Vue 组件处理
          return {};
        }
      }
    };
  },
  parseHTML() {
    return [{ tag: 'div[data-type="mermaid"]' }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, {
        'data-type': 'mermaid',
        width: HTMLAttributes.width,
        height: HTMLAttributes.height
      })
    ];
  },
  addNodeView() {
    return VueNodeViewRenderer(MermaidNodeView);
  }
});
