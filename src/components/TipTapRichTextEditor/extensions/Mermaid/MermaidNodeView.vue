<template>
  <node-view-wrapper
    class="mermaid-block"
    :style="blockStyle"
    @mouseenter="hovered = true"
    @mouseleave="hovered = false"
  >
    <div
      v-if="mode === 'edit'"
      class="mermaid-section"
      :style="{ width: width + 'px', height: height + 'px' }"
    >
      <div class="section-header">
        <span class="header-title">编辑预览</span>
        <div class="controls">
          <div class="size-controls">
            <el-input-number
              v-model="width"
              :min="MIN_SIZE"
              :step="50"
              size="small"
              style="width: 100px"
              @change="handleSizeChange"
            />
            <span class="size-separator">×</span>
            <el-input-number
              v-model="height"
              :min="MIN_SIZE"
              :step="50"
              size="small"
              style="width: 100px"
              @change="handleSizeChange"
            />
          </div>
          <el-button-group style="float: right">
            <el-button
              size="small"
              style="width: 60px; height: 30px"
              :type="previewButtonType"
              @click="switchToPreview"
              title="预览"
            >
              <Icon icon="mdi:eye" />
              预览
            </el-button>
            <el-button
              size="small"
              style="width: 60px; height: 30px"
              :type="editButtonType"
              @click="switchToEdit"
              title="编辑"
            >
              <Icon icon="mdi:pencil" />
              编辑
            </el-button>
          </el-button-group>
        </div>
      </div>
      <div class="mermaid-content">
        <textarea
          v-if="isEditing"
          v-model="code"
          @keydown.esc="cancel"
          @keydown.ctrl.enter="save"
          class="mermaid-editor"
          rows="12"
          placeholder="编辑Mermaid代码"
          autofocus
        />
        <div v-else ref="mermaidDiv" class="mermaid"></div>
      </div>
      <div class="ai-float-btn" @click="aiDialogVisible = true" title="AI生成">
        <Icon icon="mdi:robot-excited-outline" width="28" height="28" />
      </div>
      <div class="edit-action-btns">
        <el-button size="small" @click="cancel">取消</el-button>
        <el-button size="small" type="primary" @click="save">确认</el-button>
      </div>
      <div class="resize-handle" @mousedown="startResize"></div>
    </div>
    <div
      v-else
      class="mermaid-preview-area preview-only"
      :style="{ width: width + 'px', height: height + 'px' }"
    >
      <div ref="mermaidDiv" class="mermaid"></div>
      <el-button
        v-if="hovered"
        class="edit-float-btn"
        size="small"
        style="position: absolute; top: 8px; right: 12px; z-index: 2"
        @click.stop="enterEditMode"
        circle
        title="编辑"
      >
        <Icon icon="mdi:pencil" />
      </el-button>
      <div class="resize-handle" @mousedown="startResize"></div>
    </div>
    <div
      v-if="aiDialogVisible"
      class="ai-draggable-dialog"
      :style="{ left: aiDialogPos.x + 'px', top: aiDialogPos.y + 'px' }"
      @mousedown.self="bringToFront"
    >
      <div class="ai-draggable-header" @mousedown="startAIDrag">
        <span>AI生成</span>
        <el-button
          size="small"
          circle
          @click="aiDialogVisible = false"
          style="float: right"
        >
          <Icon icon="mdi:close" />
        </el-button>
      </div>
      <el-input
        v-model="aiPrompt"
        placeholder="请输入AI提示词"
        :rows="4"
        type="textarea"
        class="ai-image-input"
      />
      <div style="text-align: right; margin-top: 8px">
        <el-button size="small" @click="aiDialogVisible = false">
          取消
        </el-button>
        <el-button
          size="small"
          type="success"
          :loading="aiMermaidLoading"
          @click="generateAIMermaid"
        >
          生成
        </el-button>
      </div>
    </div>
  </node-view-wrapper>
</template>

<script setup>
import { ref, watch, onMounted, nextTick, onUnmounted, computed } from 'vue';
import { NodeViewWrapper } from '@tiptap/vue-3';
import { Icon } from '@iconify/vue';
import mermaid from 'mermaid';
import { generateImageStream } from '@/api/tender_toc_content_ai';

// 初始化 mermaid
mermaid.initialize({
  startOnLoad: false,
  theme: 'default',
  securityLevel: 'loose'
});

const props = defineProps(['node', 'updateAttributes', 'editor']);
const code = ref(props.node.attrs.code);
const mode = ref('simple');
const isEditing = ref(false);
const hovered = ref(false);
const mermaidDiv = ref(null);
const width = ref(props.node.attrs.width || 600);
const height = ref(props.node.attrs.height || 400);
const textAlign = ref(props.node.attrs.textAlign || null);

const position = ref({ x: 0, y: 0 });
const isResizing = ref(false);
const isDragging = ref(false);
const startX = ref(0);
const startY = ref(0);
const startPosX = ref(0);
const startPosY = ref(0);
const aiPrompt = ref('');
const aiMermaidLoading = ref(false);
const aiDialogVisible = ref(false);
const aiDialogPos = ref({ x: 120, y: 120 });
let aiDragStart = { x: 0, y: 0, left: 0, top: 0 };
let dragging = false;

// 定义最小尺寸，防止组件过小无法使用
const MIN_SIZE = 50;

// 计算属性确保按钮 type 的正确性
const previewButtonType = computed(() => {
  return !isEditing.value ? 'primary' : 'default';
});

const editButtonType = computed(() => {
  return isEditing.value ? 'primary' : 'default';
});

// 计算外层容器样式，让宽度跟随内部容器，并使用 margin 实现对齐
const blockStyle = computed(() => {
  const style = {
    width: width.value + 'px', // 关键：让外层容器宽度跟随内部容器
    display: 'block' // 确保是块级元素，这样 margin 对齐才能生效
  };

  // 使用 margin 实现对齐
  if (textAlign.value) {
    switch (textAlign.value) {
      case 'left':
        style.marginLeft = '0';
        style.marginRight = 'auto';
        break;
      case 'center':
        style.marginLeft = 'auto';
        style.marginRight = 'auto';
        break;
      case 'right':
        style.marginLeft = 'auto';
        style.marginRight = '0';
        break;
      case 'justify':
        style.width = '100%';
        style.marginLeft = '0';
        style.marginRight = '0';
        break;
    }
  }

  return style;
});

// 防抖处理函数
let sizeChangeTimeout = null;

function startResize(e) {
  e.preventDefault();
  e.stopPropagation();

  isResizing.value = true;
  startX.value = e.clientX;
  startY.value = e.clientY;
  startPosX.value = width.value;
  startPosY.value = height.value;

  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
}

function handleResize(e) {
  if (!isResizing.value) return;

  const deltaX = e.clientX - startX.value;
  const deltaY = e.clientY - startY.value;

  // 允许自由拖拽，只设置最小值防止尺寸过小无法使用
  const newWidth = Math.max(MIN_SIZE, startPosX.value + deltaX);
  const newHeight = Math.max(MIN_SIZE, startPosY.value + deltaY);

  width.value = newWidth;
  height.value = newHeight;
  // 在拖拽过程中不更新属性，只在拖拽结束时更新
}

function stopResize() {
  isResizing.value = false;
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);

  // 拖拽结束时更新属性
  handleSizeChange();
}

function handleSizeChange() {
  // 清除之前的定时器
  if (sizeChangeTimeout) {
    clearTimeout(sizeChangeTimeout);
  }

  // 防抖处理，避免过于频繁的更新
  sizeChangeTimeout = setTimeout(() => {
    props.updateAttributes({ width: width.value, height: height.value });
    nextTick(renderMermaid);
  }, 16); // 约60fps的更新频率
}

async function renderMermaid() {
  if (!mermaidDiv.value) return;
  try {
    mermaidDiv.value.innerHTML = '';
    const { svg } = await mermaid.render(
      'mermaid-svg-' + Date.now(),
      code.value
    );
    mermaidDiv.value.innerHTML = svg;
    // 设置 SVG 的 viewBox 以适应容器大小
    const svgElement = mermaidDiv.value.querySelector('svg');
    if (svgElement) {
      const viewBox = svgElement.getAttribute('viewBox');
      if (viewBox) {
        const [, , vbWidth, vbHeight] = viewBox.split(' ').map(Number);
        const aspectRatio = vbWidth / vbHeight;

        // 根据容器大小和宽高比调整 SVG
        const containerWidth = width.value;
        const containerHeight = height.value;
        const containerRatio = containerWidth / containerHeight;

        if (containerRatio > aspectRatio) {
          // 容器更宽，以高度为基准
          svgElement.style.width = `${containerHeight * aspectRatio}px`;
          svgElement.style.height = `${containerHeight}px`;
        } else {
          // 容器更高，以宽度为基准
          svgElement.style.width = `${containerWidth}px`;
          svgElement.style.height = `${containerWidth / aspectRatio}px`;
        }

        svgElement.style.maxWidth = '100%';
        svgElement.style.maxHeight = '100%';
        // 移除最大尺寸限制，允许 SVG 自由缩放
        svgElement.style.margin = 'auto';
      }
    }
  } catch (e) {
    mermaidDiv.value.innerHTML = `<pre style=\"color:red;\">${e.message}</pre>`;
  }
}

function save() {
  mode.value = 'simple';
  isEditing.value = false;
  setTimeout(function () {
    props.updateAttributes({ code: code.value });
    nextTick(renderMermaid);
  }, 100);
}

function cancel() {
  mode.value = 'simple';
  isEditing.value = false;
  setTimeout(function () {
    code.value = props.node.attrs.code;
    nextTick(renderMermaid);
  }, 100);
}

function enterEditMode() {
  mode.value = 'edit';
  isEditing.value = true;
  nextTick(() => {
    document.querySelector('.mermaid-editor')?.focus();
  });
}

function exitEditMode() {
  mode.value = 'simple';
  isEditing.value = false;
  setTimeout(function () {
    nextTick(renderMermaid);
  }, 100);
}

function switchToPreview() {
  isEditing.value = false;
  setTimeout(function () {
    nextTick(renderMermaid);
  }, 100);
}

function switchToEdit() {
  if (isEditing.value) return;
  isEditing.value = true;
  nextTick(() => {
    document.querySelector('.mermaid-editor')?.focus();
  });
}

async function generateAIMermaid() {
  if (!aiPrompt.value.trim()) return;
  aiMermaidLoading.value = true;
  // 模拟AI接口调用
  // 这里可以替换为真实的AI接口返回内容
  generateImageStream({ content: aiPrompt.value }, function (text) {
    code.value = text;
    aiMermaidLoading.value = false;
    if (!isEditing.value) {
      setTimeout(function () {
        nextTick(renderMermaid);
      }, 100);
    }
  });
}

function startAIDrag(e) {
  dragging = true;
  aiDragStart = {
    x: e.clientX,
    y: e.clientY,
    left: aiDialogPos.value.x,
    top: aiDialogPos.value.y
  };
  document.addEventListener('mousemove', onAIDrag);
  document.addEventListener('mouseup', stopAIDrag);
}

function onAIDrag(e) {
  if (!dragging) return;
  aiDialogPos.value.x = aiDragStart.left + (e.clientX - aiDragStart.x);
  aiDialogPos.value.y = aiDragStart.top + (e.clientY - aiDragStart.y);
}

function stopAIDrag() {
  dragging = false;
  document.removeEventListener('mousemove', onAIDrag);
  document.removeEventListener('mouseup', stopAIDrag);
}

function bringToFront() {
  // Implement the logic to bring the AI dialog to the front
}

watch(
  () => props.node.attrs.code,
  (val) => {
    code.value = val;
    if (!isEditing.value)
      setTimeout(function () {
        nextTick(renderMermaid);
      }, 100);
  }
);

watch(
  () => props.node.attrs.textAlign,
  (val) => {
    textAlign.value = val;
  }
);

watch(isEditing, (val) => {
  if (!val)
    setTimeout(function () {
      nextTick(renderMermaid);
    }, 100);
});

watch(aiDialogVisible, (visible) => {
  if (visible) {
    // 弹窗宽度和高度要与 .ai-draggable-dialog 样式一致
    const dialogWidth = 480;
    const dialogHeight = 260; // 你可以根据实际内容调整
    aiDialogPos.value.x = Math.max(0, (window.innerWidth - dialogWidth) / 2);
    aiDialogPos.value.y = Math.max(0, (window.innerHeight - dialogHeight) / 2);
  }
});

onMounted(() => {
  if (!isEditing.value) renderMermaid();
});

onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);

  // 清理定时器
  if (sizeChangeTimeout) {
    clearTimeout(sizeChangeTimeout);
  }
});
</script>

<style scoped>
.mermaid-block,
.mermaid-block.ProseMirror-selectednode {
  /* display: inline-block !important; */
  /* width: auto !important; */
  vertical-align: middle;
  box-sizing: border-box;
  border-radius: 12px;
  background: transparent;
}
.mermaid-block.ProseMirror-selectednode {
  box-shadow: 0 0 0 2px #409eff;
}
.drag-handle {
  cursor: grab;
  color: #bbb;
  font-size: 20px;
  margin-right: 8px;
  flex-shrink: 0;
}
.mermaid-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 0;
  position: relative;
  box-sizing: border-box;
  cursor: move;
}
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}
.header-title {
  font-weight: bold;
  font-size: 16px;
  margin-right: 16px;
}
.controls {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: flex-end;
}
.size-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}
.size-separator {
  color: #666;
}
.mermaid-content {
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  height: calc(100% - 60px);
  overflow: visible;
  box-sizing: border-box;
}
.mermaid-editor-container {
  height: 100%;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
}
.mermaid-preview-area {
  background: transparent;
  border: none;
  padding: 0;
  position: relative;
  overflow: visible;
  box-sizing: border-box;
  cursor: move;
}
.mermaid-preview-area.preview-only {
  background: transparent;
  border: none;
  padding: 0;
}
.mermaid {
  width: 100%;
  height: 100%;
  background: transparent;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mermaid > svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: block;
  margin: 0;
  box-sizing: border-box;
}
.resize-handle {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 20px;
  height: 20px;
  cursor: se-resize;
  background: linear-gradient(135deg, transparent 50%, #ccc 50%);
  border-radius: 0 0 4px 0;
  z-index: 1000;
  user-select: none;
}
.resize-handle:hover {
  background: linear-gradient(135deg, transparent 50%, #999 50%);
}
.edit-float-btn {
  position: absolute;
  top: 8px;
  right: 12px;
  z-index: 2;
  background: #fff;
  border: 1px solid #dcdcdc;
  border-radius: 50%;
  padding: 2px 8px;
  font-size: 13px;
  cursor: pointer;
  transition: background 0.2s;
}
.edit-float-btn:hover {
  background: #f0f0f0;
}
.mermaid-editor {
  width: 100%;
  height: 100%;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
  font-size: 15px;
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  padding: 12px;
  background: #f8f8fa;
  resize: none;
  outline: none;
  box-sizing: border-box;
}
.section-header .el-button-group .el-button {
  border-radius: 2px !important;
  min-width: 32px;
  padding: 0 8px;
}
.ai-float-btn {
  position: absolute;
  right: 18px;
  bottom: 48px;
  z-index: 10;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #e0e0e0;
  transition:
    box-shadow 0.2s,
    background 0.2s;
}
.ai-float-btn:hover {
  background: #f6f8fa;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}
.ai-image-input {
  width: 100%;
  margin-bottom: 8px;
}
.ai-draggable-dialog {
  position: fixed;
  z-index: 9999;
  width: 480px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
  border: 1px solid #e0e0e0;
  padding: 0 16px 16px 16px;
  user-select: none;
}
.ai-draggable-header {
  cursor: move;
  font-weight: bold;
  font-size: 15px;
  padding: 10px 0 8px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.edit-action-btns {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 12px;
}
.mermaid .cluster rect {
  fill: transparent !important;
}
</style>
