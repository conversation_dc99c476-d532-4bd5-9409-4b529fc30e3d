<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar" v-if="editor">
      <!-- 基础格式 -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBold().run()"
          :class="{ 'is-active': editor.isActive('bold') }"
        >
          <Icon icon="mdi:format-bold" />
        </button>
        <button
          @click="editor.chain().focus().toggleItalic().run()"
          :class="{ 'is-active': editor.isActive('italic') }"
        >
          <Icon icon="mdi:format-italic" />
        </button>
        <button
          @click="editor.chain().focus().toggleUnderline().run()"
          :class="{ 'is-active': editor.isActive('underline') }"
        >
          <Icon icon="mdi:format-underline" />
        </button>
        <button
          @click="editor.chain().focus().toggleStrike().run()"
          :class="{ 'is-active': editor.isActive('strike') }"
        >
          <Icon icon="mdi:format-strikethrough-variant" />
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 字体设置 -->
      <div class="toolbar-group">
        <el-select
          v-model="fontFamily"
          @change="setFontFamily"
          size="small"
          style="width: 120px"
        >
          <el-option
            v-for="font in fontFamilies"
            :key="font.value"
            :label="font.label"
            :value="font.value"
          />
        </el-select>
        <el-select
          v-model="fontSize"
          @change="setFontSize"
          size="small"
          style="width: 80px"
        >
          <el-option
            v-for="size in fontSizes"
            :key="size.value"
            :label="size.label"
            :value="size.value"
          />
        </el-select>
      </div>

      <!-- 颜色设置 -->
      <div class="toolbar-group">
        <el-color-picker
          v-model="textColor"
          @change="(color) => editor.chain().focus().setColor(color).run()"
          show-alpha
          size="small"
          style="margin: 0 6px"
        >
          字体
        </el-color-picker>
        <div class="toolbar-divider"></div>
        <el-color-picker
          v-model="highlightColor"
          @change="
            (color) => editor.chain().focus().setHighlight({ color }).run()
          "
          show-alpha
          size="small"
          style="margin: 0 6px"
        >
          背景
        </el-color-picker>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 对齐方式 -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().setTextAlign('left').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'left' }) }"
        >
          <Icon icon="mdi:format-align-left" />
        </button>
        <button
          @click="editor.chain().focus().setTextAlign('center').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'center' }) }"
        >
          <Icon icon="mdi:format-align-center" />
        </button>
        <button
          @click="editor.chain().focus().setTextAlign('right').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'right' }) }"
        >
          <Icon icon="mdi:format-align-right" />
        </button>
        <button
          @click="editor.chain().focus().setTextAlign('justify').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'justify' }) }"
        >
          <Icon icon="mdi:format-align-justify" />
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 列表 -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="{ 'is-active': editor.isActive('bulletList') }"
        >
          <Icon icon="mdi:format-list-bulleted" />
        </button>
        <button
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="{ 'is-active': editor.isActive('orderedList') }"
        >
          <Icon icon="mdi:format-list-numbered" />
        </button>
        <button
          @click="editor.chain().focus().toggleTaskList().run()"
          :class="{ 'is-active': editor.isActive('taskList') }"
        >
          <Icon icon="mdi:format-list-checkbox" />
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 缩进 -->
      <div class="toolbar-group">
        <button @click="increaseIndent">
          <Icon icon="mdi:format-indent-increase" />
        </button>
        <button @click="decreaseIndent">
          <Icon icon="mdi:format-indent-decrease" />
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 其他格式 -->
      <div class="toolbar-group">
        <button @click="openDrawer">
          <Icon icon="mdi:image-outline" />
        </button>
        <button @click="insertTable">
          <Icon icon="mdi:table" />
        </button>
        <button @click="openMermaidDialog" style="font-size: 14px">
          <Icon icon="mdi:chart-timeline-variant" />
          AI图表
        </button>
        <button
          @click="toggleFullscreen"
          :class="{ 'is-active': isFullscreen }"
        >
          <Icon icon="mdi:fullscreen" />
        </button>
      </div>

      <div class="toolbar-group">
        <div v-if="editor?.isActive('table')" class="table-toolbar">
          <button @click="addColumnBefore">
            <Icon icon="mdi:table-column-plus-before" />
          </button>
          <button @click="addColumnAfter">
            <Icon icon="mdi:table-column-plus-after" />
          </button>
          <button @click="deleteColumn">
            <Icon icon="mdi:table-column-remove" />
          </button>
          <button @click="addRowBefore">
            <Icon icon="mdi:table-row-plus-before" />
          </button>
          <button @click="addRowAfter">
            <Icon icon="mdi:table-row-plus-after" />
          </button>
          <button @click="deleteRow">
            <Icon icon="mdi:table-row-remove" />
          </button>
          <button @click="mergeCells">
            <Icon icon="mdi:table-merge-cells" />
          </button>
          <button @click="splitCell">
            <Icon icon="mdi:table-split-cell" />
          </button>
          <button @click="toggleHeaderColumn">
            <Icon icon="mdi:table-column" />
          </button>
          <button @click="toggleHeaderRow">
            <Icon icon="mdi:table-row" />
          </button>
          <button @click="deleteTable">
            <Icon icon="mdi:table-remove" />
          </button>
        </div>
      </div>
    </div>
    <div class="editor-content-wrapper">
      <editor-content :editor="editor" class="editor-content" />
      <div
        v-if="showAITips"
        class="ai-tips-group"
        :style="{
          position: 'absolute',
          top: aiTipsPos.top + 'px',
          left: aiTipsPos.left + 'px',
          zIndex: 9999
        }"
      >
        <Button class="ai-tips-btn ai-tips-btn-left" @click="openAIDialog">
          AI
        </Button>
        <Dropdown>
          <Button
            class="ai-tips-btn ai-tips-btn-right"
            icon="md-create"
          ></Button>
          <template #list>
            <DropdownMenu style="width: 60px">
              <DropdownItem @click="openAIDialog(1)">改写</DropdownItem>
              <DropdownItem @click="openAIDialog(2)">扩写</DropdownItem>
              <DropdownItem @click="openAIDialog(3)">润色</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
      </div>
    </div>
    <TocContentImageSettingDrawerModal
      v-model="showImageDrawer"
      @close="showImageDrawer = false"
      @add-image="handleAddImage"
    />

    <AIGenerateMermaidDialog
      v-model:visible="showMermaidDialog"
      :mermaid-code="mermaidCode"
      @update:mermaid-code="(val) => (mermaidCode = val)"
      @insert-mermaid="insertMermaid"
    />
    <AIAssistantDialog
      v-if="showAIDialog"
      @close="showAIDialog = false"
      @send="handleAIGenerate"
      :ai-handle-content="aiHandleContent"
      :ai-edit-type="aiEditType"
    />
    <div
      v-if="showAIFloatBtn"
      :style="{
        position: 'absolute',
        top: aiBtnPos.top + 'px',
        left: aiBtnPos.left + 'px',
        zIndex: 9999
      }"
    >
      <el-button type="primary" size="small" @click="showAIDialog = true">
        AI助力
      </el-button>
    </div>
  </div>
</template>

<script>
import { Editor, EditorContent } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Strike from '@tiptap/extension-strike';
import TextAlign from '@tiptap/extension-text-align';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import Link from '@tiptap/extension-link';
import ImageWithNodeView from './extensions/Image/ImageWithNodeView';
import CustomTable from './extensions/Table/CustomTable';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import FontSize from 'tiptap-extension-font-size';
import CustomOrderedList from './extensions/OrderedList/CustomOrderedList';
import { Icon } from '@iconify/vue';
import { ElMessage } from 'element-plus';
import MermaidNodeView from './extensions/Mermaid/MermaidNodeView';
import AIGenerateMermaidDialog from './extensions/Mermaid/AIGenerateMermaidDialog.vue';
import AIAssistantDialog from './extensions/AI/AIAssistantDialog.vue';
import { Node } from '@tiptap/core';
import CustomParagraph from './extensions/Paragraph/CustomParagraph';
import TocContentImageSettingDrawerModal from '@/views/tender/modal/TocContentImageSettingDrawerModal.vue';
const INDENT_STEP = 24; // 单位pt
async function imageUrlToBase64(imageUrl) {
  const response = await fetch(imageUrl);
  const blob = await response.blob();
  return await new Promise((resolve) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result);
    reader.readAsDataURL(blob);
  });
}

export default {
  name: 'RichTextEditor',
  components: {
    EditorContent,
    Icon,
    AIGenerateMermaidDialog,
    AIAssistantDialog,
    TocContentImageSettingDrawerModal
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeMenu: 'tender_case',
      imageListTemp: [],
      previewVisible: false,
      previewUrl: '',
      previewImgUrl: '',
      showImageDrawer: false,
      editor: null,
      textColor: '#000000',
      highlightColor: '#ffff00',
      fontFamily: '宋体',
      fontSize: '16px',
      fontFamilies: [
        { label: '默认字体', value: '默认字体' },
        { label: '宋体', value: '宋体' },
        { label: '黑体', value: '黑体' },
        { label: '微软雅黑', value: '微软雅黑' },
        { label: '楷体', value: '楷体' },
        { label: '仿宋', value: '仿宋' },
        { label: '华文仿宋', value: '华文仿宋' },
        { label: '华文楷体', value: '华文楷体' },
        { label: 'Times New Roman', value: 'Times New Roman' },
        { label: 'Arial', value: 'Arial' },
        { label: 'Verdana', value: 'Verdana' }
      ],
      fontSizes: [
        { label: '12px', value: '12px' },
        { label: '14px', value: '14px' },
        { label: '16px', value: '16px' },
        { label: '18px', value: '18px' },
        { label: '20px', value: '20px' },
        { label: '24px', value: '24px' },
        { label: '28px', value: '28px' },
        { label: '32px', value: '32px' },
        { label: '36px', value: '36px' },
        { label: '48px', value: '48px' }
      ],
      showMermaidDialog: false,
      mermaidCode: '',
      mermaidPreview: '',
      mermaidSvgError: '',
      aiImagePrompt: '',
      aiImageUrl: '',
      aiImageLoading: false,
      aiImagePreviewLarge: false,
      mermaidViewMode: 'preview',
      aiMermaidLoading: false,
      showAIFloatBtn: false,
      aiBtnPos: { top: 0, left: 0 },
      showAIDialog: false,
      showAITips: false,
      aiTipsPos: { top: 0, left: 0 },
      aiEditType: '',
      aiHandleContent: '',
      isFullscreen: false,
      root: null,
      tableStyles: {
        borderColor: '#000000',
        backgroundColor: '#ffffff',
        borderWidth: '1px',
        borderStyle: 'solid'
      },
      isUpdatingFromEditor: false, // 添加标志位避免死循环
      updateTimeout: null // 添加防抖控制
    };
  },
  watch: {
    modelValue: {
      handler(newContent) {
        if (this.editor && !this.isUpdatingFromEditor) {
          this.safeSetContent(newContent);
        }
      },
      immediate: false // 改为 false，避免初始化冲突
    },
    mermaidCode: {
      immediate: true,
      async handler(newCode) {
        if (newCode) {
          try {
            const { svg } = await MermaidNodeView.render(
              'mermaid-preview',
              newCode
            );
            this.mermaidPreview = svg;
            this.mermaidSvgError = '';
          } catch (error) {
            this.mermaidPreview = '';
            this.mermaidSvgError = 'Mermaid 语法错误或渲染失败！';
            console.error('Mermaid preview error:', error);
          }
        } else {
          this.mermaidPreview = '';
          this.mermaidSvgError = '';
        }
      }
    }
  },
  mounted() {
    this.initEditor();

    // 等待编辑器完全初始化后再设置初始内容
    this.$nextTick(() => {
      if (this.modelValue && this.editor) {
        this.safeSetContent(this.modelValue);
      }
    });

    // 监听编辑器滚动事件
    const editorContent = this.$el.querySelector('.editor-content');
    if (editorContent) {
      editorContent.addEventListener('scroll', this.handleScroll);
    }
    this.editor.on('selectionUpdate', ({ editor }) => {
      const selection = window.getSelection();
      if (selection && !selection.isCollapsed) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        this.showAITips = true;
        this.aiTipsPos = this.calculateBtnPos(rect);
      } else {
        this.showAITips = false;
      }
    });
  },
  beforeUnmount() {
    // 清理定时器
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout);
    }

    if (this.editor) {
      this.editor.destroy();
    }
    // 移除滚动事件监听
    const editorContent = this.$el.querySelector('.editor-content');
    if (editorContent) {
      editorContent.removeEventListener('scroll', this.handleScroll);
    }
  },
  methods: {
    handleScroll() {
      if (this.showAITips) {
        const selection = window.getSelection();
        if (selection && !selection.isCollapsed) {
          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();
          this.aiTipsPos = this.calculateBtnPos(rect);
        }
      }
    },
    initEditor() {
      this.editor = new Editor({
        extensions: [
          StarterKit.configure({
            orderedList: false // 禁用默认的有序列表
          }),
          Underline,
          Strike,
          TextStyle,
          TextAlign.configure({
            types: ['heading', 'paragraph', 'mermaid', 'image']
          }),
          Color.configure({ types: ['textStyle'] }),
          Highlight.configure({ types: ['textStyle'] }),
          FontFamily.configure({ types: ['textStyle'] }),
          FontSize.configure({ types: ['textStyle'] }),
          Link.configure({
            openOnClick: false,
            HTMLAttributes: {
              rel: 'noopener noreferrer',
              class: 'editor-link'
            }
          }),
          CustomTable.configure({
            resizable: true,
            columnResizing: true,
            rowResizing: true,
            cellSelection: true,
            tableSelection: true
          }),
          TableRow,
          TableCell,
          TableHeader,
          ImageWithNodeView.configure({
            HTMLAttributes: {
              class: 'editor-image'
            },
            draggable: true,
            resizable: true,
            inline: false,
            allowBase64: true
          }),
          TaskList,
          TaskItem,
          MermaidNodeView.configure({
            HTMLAttributes: {
              class: 'mermaid-diagram'
            }
          }),
          // CustomParagraph, // 注释掉，因为它与 TextAlign 扩展冲突
          CustomOrderedList
        ],
        content: this.modelValue,
        onUpdate: ({ editor }) => {
          // 清除之前的超时
          if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
          }

          var html = editor.getHTML();
          html = html.replace(/ {2,}/g, (match) => {
            return '&nbsp;'.repeat(match.length);
          });

          this.isUpdatingFromEditor = true;
          this.$emit('update:modelValue', html);

          // 使用防抖重置标志位
          this.updateTimeout = setTimeout(() => {
            this.isUpdatingFromEditor = false;
          }, 100); // 100ms 防抖
        }
      });
    },
    // 添加内容比较方法
    isContentDifferent(newContent) {
      if (!this.editor) return true;

      const currentContent = this.editor.getHTML();
      // 标准化内容进行比较（移除空白差异）
      const normalize = (content) => content.replace(/\s+/g, ' ').trim();

      return normalize(currentContent) !== normalize(newContent);
    },

    // 安全的内容更新方法
    safeSetContent(content) {
      if (!this.editor || !this.isContentDifferent(content)) {
        return;
      }

      // 保存编辑状态
      const { from, to } = this.editor.state.selection;
      const hasFocus = this.editor.isFocused;

      // 更新内容
      this.editor.commands.setContent(content);

      // 恢复编辑状态
      this.$nextTick(() => {
        try {
          if (hasFocus) {
            this.editor.commands.focus();
            // 尝试恢复选择
            if (from !== to) {
              this.editor.commands.setTextSelection({ from, to });
            }
          }
        } catch (e) {
          console.warn('Failed to restore editor state:', e);
        }
      });
    },
    setFontFamily(font) {
      this.editor.chain().focus().setFontFamily(font).run();
    },
    setFontSize(size) {
      this.editor.chain().focus().setFontSize(size).run();
    },
    dleClosePreview() {
      this.previewDialogVisible = false;
      this.previewImgUrl = '';
    },
    handlePreview(url) {
      this.previewImgUrl = url;
      this.previewDialogVisible = true;
    },
    async handleAddImage(imageUrl) {
      console.log(imageUrl);
      try {
        this.editor.chain().focus().setImage({ src: imageUrl }).run();
      } catch (e) {
        console.log(e);
      }
    },
    async openDrawer() {
      this.showImageDrawer = true;
    },
    insertTable() {
      this.editor
        .chain()
        .focus()
        .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
        .run();
    },
    openMermaidDialog() {
      this.mermaidCode = '';
      this.showMermaidDialog = true;
    },
    insertMermaid() {
      if (this.mermaidCode.trim()) {
        this.editor
          .chain()
          .focus()
          .insertContent({
            type: 'mermaid',
            attrs: { code: this.mermaidCode }
          })
          .run();
        this.mermaidCode = '';
      }
      this.showMermaidDialog = false;
    },
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
      if (this.isFullscreen) {
        this.$el.classList.add('fullscreen');
        document.body.style.overflow = 'hidden';
      } else {
        this.$el.classList.remove('fullscreen');
        document.body.style.overflow = '';
      }
    },
    handleAIGenerate(prompt) {
      // 这里处理AI生成逻辑
      const cleanText = prompt
        .trim()
        .replace(/<[^>]*>/g, '')
        .replace(/&nbsp;/g, ' ');

      if (cleanText) {
        // 插入生成的文本
        this.editor.chain().focus().insertContent(cleanText).run();
        this.showAIDialog = false;
      }
    },
    calculateBtnPos(rect) {
      const btnHeight = 40; // 气泡高度
      const btnWidth = 100; // 气泡宽度
      const margin = 0;
      const gap = 5; // 选区和气泡之间的间隔

      // 获取编辑器容器
      const container = this.$el.querySelector('.editor-content');
      const containerRect = container.getBoundingClientRect();

      // 计算相对于容器的位置
      const relativeTop = rect.top - containerRect.top;
      const relativeLeft = rect.left - containerRect.left;

      // 计算图标位置 - 优先显示在选区下方
      let top = relativeTop + rect.height + gap;
      let left = relativeLeft + rect.width / 2 - btnWidth / 2;

      // 如果下方空间不够，显示在上方
      const spaceBelow = containerRect.height - (relativeTop + rect.height);
      if (spaceBelow < btnHeight + gap) {
        top = relativeTop - btnHeight - gap;
      }

      // 边界修正
      left = Math.max(
        margin,
        Math.min(left, container.clientWidth - btnWidth - margin)
      );
      top = Math.max(
        margin,
        Math.min(top, container.clientHeight - btnHeight - margin)
      );

      return { top, left };
    },
    openAIDialog(type) {
      this.aiEditType = type;
      this.showAIDialog = true;
      // 获取选中的内容
      const { from, to } = this.editor.state.selection;
      if (from !== to) {
        // 有选中内容
        this.aiHandleContent = this.editor.state.doc.textBetween(from, to);
      } else {
        // 没有选中内容，获取全部
        this.aiHandleContent = this.editor.getHTML();
      }
    },
    addColumnBefore() {
      this.editor.chain().focus().addColumnBefore().run();
    },
    addColumnAfter() {
      this.editor.chain().focus().addColumnAfter().run();
    },
    deleteColumn() {
      this.editor.chain().focus().deleteColumn().run();
    },
    addRowBefore() {
      this.editor.chain().focus().addRowBefore().run();
    },
    addRowAfter() {
      this.editor.chain().focus().addRowAfter().run();
    },
    deleteRow() {
      this.editor.chain().focus().deleteRow().run();
    },
    mergeCells() {
      this.editor.chain().focus().mergeCells().run();
    },
    splitCell() {
      this.editor.chain().focus().splitCell().run();
    },
    toggleHeaderColumn() {
      this.editor.chain().focus().toggleHeaderColumn().run();
    },
    toggleHeaderRow() {
      this.editor.chain().focus().toggleHeaderRow().run();
    },
    deleteTable() {
      this.editor.chain().focus().deleteTable().run();
    },
    setTableStyle(style) {
      this.editor.chain().focus().updateAttributes('table', style).run();
    },
    increaseIndent() {
      const { state, view } = this.editor;
      const { from, to } = state.selection;
      state.doc.nodesBetween(from, to, (node, pos) => {
        if (node.type.name === 'paragraph') {
          let style = node.attrs.style || '';
          let matches = [
            ...style.matchAll(/margin-left:\s*(\d+(?:\.\d+)?)pt/g)
          ];
          let current =
            matches.length > 0 ? parseFloat(matches[matches.length - 1][1]) : 0;
          let next = current + INDENT_STEP;
          let newStyle = style
            .replace(/margin-left:\s*\d+(?:\.\d+)?pt;?/g, '')
            .trim();
          newStyle =
            (newStyle ? newStyle + '; ' : '') + `margin-left: ${next}pt;`;
          this.editor.commands.updateAttributes('paragraph', {
            style: newStyle
          });
        }
      });
    },

    decreaseIndent() {
      const { state, view } = this.editor;
      const { from, to } = state.selection;
      state.doc.nodesBetween(from, to, (node, pos) => {
        if (node.type.name === 'paragraph') {
          this.editor.commands.updateAttributes('paragraph', { style: null });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.rich-text-editor {
  width: 100%;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;

  .editor-toolbar {
    display: flex;
    gap: 2px;
    padding: 8px;
    background: #f4f6fa;
    border-bottom: 1px solid #e8eaec;
    flex-wrap: wrap;
    align-items: center;

    .toolbar-group {
      display: flex;
      gap: 2px;
      align-items: center;
    }

    .toolbar-divider {
      width: 1px;
      height: 22px;
      background: #e0e0e0;
      margin: 0 6px;
    }

    button {
      padding: 0 10px;
      height: 32px;
      border: none;
      background: transparent;
      border-radius: 5px;
      color: #222;
      font-size: 18px;
      cursor: pointer;
      transition:
        background 0.2s,
        color 0.2s;
      outline: none;
      display: flex;
      align-items: center;

      &:hover {
        background: #e6f0fa;
        color: #2d8cf0;
      }

      &.is-active {
        background: #2d8cf0;
        color: #fff;
      }
    }
  }

  .editor-content-wrapper {
    height: 100%;
    overflow-y: auto;
  }
  .editor-content {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    pointer-events: auto;

    :deep(.ProseMirror) {
      outline: none;
      height: 100%;
      min-height: 0;
      cursor: text;
      pointer-events: auto;

      ul,
      ol {
        padding: 0 1rem;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        line-height: 1.1;
      }

      code {
        background-color: rgba(97, 97, 97, 0.1);
        color: #616161;
        padding: 0.25em;
        border-radius: 0.25em;
      }

      pre {
        background: #0d0d0d;
        color: #fff;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;

        code {
          color: inherit;
          padding: 0;
          background: none;
          font-size: 0.8rem;
        }
      }

      img {
        max-width: 100%;
        height: auto;
        cursor: pointer;
      }

      hr {
        border: none;
        border-top: 2px solid rgba(13, 13, 13, 0.1);
        margin: 2rem 0;
      }

      .editor-link {
        color: #2d8cf0;
        text-decoration: underline;
      }

      table {
        border-collapse: collapse;
        margin: 0;
        overflow: visible;
        background: #fff;
        user-select: text;
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        position: relative;
        width: 100%;
        table-layout: auto;

        td,
        th {
          border: 1px solid #e5e7eb;
          padding: 12px 16px;
          text-align: left;
          vertical-align: top;
          min-width: 80px;
          position: relative;
          transition: all 0.2s ease;
          user-select: text;
          -webkit-user-select: text;
          -moz-user-select: text;
          -ms-user-select: text;
          box-sizing: border-box;

          > * {
            margin-bottom: 0;
          }
        }

        th {
          background: linear-gradient(135deg, #f8fafc, #f1f5f9);
          font-weight: 600;
          color: #374151;
          border-bottom: 2px solid #e5e7eb;
        }

        td {
          background: #fff;
          color: #1f2937;
        }

        /* 单元格悬停效果 */
        td:hover,
        th:hover {
          background-color: #f9fafb;
          box-shadow: inset 0 0 0 2px #3b82f6;
        }

        /* 单元格选中效果 */
        td.selected,
        th.selected {
          background-color: #dbeafe !important;
          box-shadow: inset 0 0 0 2px #3b82f6;
        }

        tr:nth-child(even) td:hover {
          background-color: #f3f4f6;
        }

        /* 选中状态样式 */
        .selectedCell {
          background-color: #dbeafe !important;
          box-shadow: inset 0 0 0 2px #3b82f6;
        }

        .selectedCell::after {
          background: rgba(59, 130, 246, 0.1);
          content: '';
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          pointer-events: none;
          position: absolute;
          z-index: 2;
        }

        /* 列宽调整手柄 */
        .column-resize-handle {
          position: absolute;
          top: 0;
          right: 0;
          width: 4px;
          height: 100%;
          cursor: col-resize;
          background-color: transparent;
          z-index: 5;
          transition: background-color 0.2s ease;
        }

        .column-resize-handle:hover {
          background-color: #3b82f6;
        }

        .column-resize-handle:active {
          background-color: #1d4ed8;
        }

        /* 行高调整手柄 */
        .row-resize-handle {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 4px;
          cursor: row-resize;
          background-color: transparent;
          z-index: 5;
          transition: background-color 0.2s ease;
        }

        .row-resize-handle:hover {
          background-color: #3b82f6;
        }

        .row-resize-handle:active {
          background-color: #1d4ed8;
        }
      }

      .tableWrapper {
        margin: 1.5rem 0;
        overflow-x: auto;
        position: relative;
      }

      &.resize-cursor {
        cursor: ew-resize;
        cursor: col-resize;
      }

      /* 表格拖拽时的样式 */
      .table-container.resizing {
        cursor: nw-resize;
      }

      .table-container.column-resizing {
        cursor: col-resize;
      }

      .table-container.row-resizing {
        cursor: row-resize;
      }

      [style*='font-family'] {
        font-family: var(--font-family);
      }

      [style*='font-size'] {
        font-size: var(--font-size);
      }

      .mermaid-diagram {
        margin: 1em 0;
        padding: 1em;
        background: #f8f9fa;
        border-radius: 4px;
        overflow-x: auto;

        svg {
          max-width: 100%;
          height: auto;
        }
      }

      // 自定义有序列表样式
      ol {
        counter-reset: list-counter;
        list-style: none;
        padding-left: 1.5em;

        li {
          position: relative;
          counter-increment: list-counter;

          &::before {
            content: counter(list-counter, decimal) '.';
            position: absolute;
            left: -1.5em;
            font-weight: bold;
            color: #333;
          }
        }
      }

      /* 响应式表格样式 */
      @media (max-width: 768px) {
        table {
          td,
          th {
            padding: 8px 12px;
            min-width: 60px;
            font-size: 14px;
          }

          .column-resize-handle {
            width: 3px;
          }

          .row-resize-handle {
            height: 3px;
          }
        }
      }
    }
  }
}

.mermaid-ai-dialog {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.mermaid-section {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 0;
}
.section-header {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.mermaid-input,
.mermaid-preview-area {
  min-height: 180px;
  max-height: 320px;
  height: 240px;
  overflow: auto;
  box-sizing: border-box;
}
.mermaid-preview-area {
  background: #fff;
  border-radius: 4px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  margin-bottom: 0;
  :deep(svg) {
    max-width: 400px;
    max-height: 220px;
    width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
  }
}
.mermaid-error {
  color: #e53935;
  font-size: 14px;
}
.ai-section {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  margin-top: 0;
}
.ai-image-input {
  margin-bottom: 8px;
}
.ai-image-preview-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 8px;
  .ai-image-toolbar {
    position: absolute;
    top: 6px;
    right: 6px;
    z-index: 2;
  }
  img {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    &.large {
      max-width: none;
      width: 100%;
      max-height: 60vh;
      object-fit: contain;
      background: #fff;
      z-index: 1;
    }
  }
}
.section-header .el-button-group .el-button {
  border-radius: 2px !important;
  min-width: 32px;
  padding: 0 8px;
}
.ai-btn-group {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
  flex-direction: row;
  gap: 8px;
}
.ai-result {
  margin-top: 16px;
  background: #f6f8fa;
  border-radius: 6px;
  padding: 12px;
  color: #333;
  font-size: 15px;
}
.ai-tips-group {
  display: inline-flex;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  background: #fff;
}
.ai-tips-btn {
  border-radius: 0;
  border-right: 1px solid #e0e0e0;
  margin: 0;
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
}
.ai-tips-btn-left {
  width: 45px;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}
.ai-tips-btn-right {
  width: 45px;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border-right: none;
}

/deep/ .ivu-dropdown-menu {
  min-width: 70px !important;
  /* 或者 width: 120px; */
}

.fullscreen {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  background: #fff;
  width: 100vw;
  height: 100vh;
}

.table-toolbar {
  display: inline-flex;
  gap: 4px;
  margin-left: 8px;
  padding: 6px;
  background: #f4f6fa;
  border-radius: 6px;
  border: 1px solid #e8eaec;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  button {
    padding: 6px;
    height: 28px;
    width: 36px;
    font-size: 20px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #e6f0fa;
      color: #2d8cf0;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.ProseMirror .tableWrapper {
  overflow-x: auto;
  position: relative;
}
.ProseMirror .tableWrapper .resize-cursor {
  cursor: col-resize;
}
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.image-item {
  position: relative;
  width: 200px;
  height: 150px;
  border: 1px solid #eee;
  border-radius: 6px;
  background: #fff;
}
.image-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.image-mask {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  gap: 8px;
  opacity: 0;
}
.image-item:hover .image-mask {
  opacity: 1;
}
.sidebar-btn {
  width: 120px;
  text-align: left;
}

/* 图片tabs样式 */
.image-tabs {
  width: 100%;

  :deep(.el-tabs__header) {
    margin-bottom: 12px;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0;
  }

  :deep(.el-tabs__item) {
    padding: 8px 12px;
    font-size: 14px;
    height: 36px;
    line-height: 20px;
    border-radius: 6px;
    margin-right: 4px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f0f2f5;
    }

    &.is-active {
      background-color: #2d8cf0;
      color: #fff;
    }
  }

  :deep(.el-tabs__active-bar) {
    display: none;
  }

  :deep(.el-tabs__content) {
    padding: 16px 0;
  }
}
</style>
