# CustomParagraph 扩展与 TextAlign 冲突分析

## 🔍 问题根源

### CustomParagraph 扩展的问题
`CustomParagraph` 扩展重新定义了 `paragraph` 节点，这与 TipTap 的 `TextAlign` 扩展产生了冲突。

### 关键冲突点

#### 1. **节点重定义冲突**
```javascript
// CustomParagraph.js
const CustomParagraph = Node.create({
  name: 'paragraph',  // ❌ 重新定义了 paragraph 节点
  group: 'block',
  content: 'inline*',
  // ...
})
```

**问题**：
- TipTap 的 `TextAlign` 扩展依赖于标准的 `paragraph` 节点
- `CustomParagraph` 重新定义了 `paragraph` 节点，覆盖了默认行为
- 扩展加载顺序：`TextAlign` → `CustomParagraph`，后者覆盖前者

#### 2. **样式处理机制冲突**
```javascript
// CustomParagraph 的样式处理
addAttributes() {
  return {
    style: {
      parseHTML: element => {
        const style = element.getAttribute('style');
        // 只允许安全样式，过滤 background 和 color
        const notAllowed = ['background', 'color'];
        return style
          .split(';')
          .filter(s => {
            const key = s.split(':')[0].trim();
            return !notAllowed.some(na => key === na || key.startsWith(na + '-'));
          })
          .join(';');
      }
    }
  }
}
```

**问题分析**：
- 虽然 `text-align` 不在过滤列表中，但 `CustomParagraph` 的样式处理机制可能与 `TextAlign` 扩展的样式应用机制冲突
- `TextAlign` 扩展期望使用标准的属性处理方式，但 `CustomParagraph` 提供了自定义的样式处理

#### 3. **扩展集成问题**
```javascript
// 扩展加载顺序
extensions: [
  // ...
  TextAlign.configure({
    types: ['heading', 'paragraph', 'mermaid']
  }),
  // ...
  CustomParagraph,  // ❌ 在 TextAlign 之后加载，覆盖了 paragraph 节点
]
```

## 🛠️ 解决方案

### 方案1：注释掉 CustomParagraph（推荐）
```javascript
// 注释掉 CustomParagraph 扩展
// CustomParagraph, // 注释掉，因为它与 TextAlign 扩展冲突
```

**优点**：
- 立即解决 TextAlign 功能问题
- 使用 TipTap 标准的 paragraph 节点
- 避免扩展冲突

**影响评估**：
- 失去自定义的样式过滤功能
- 可能影响某些特定的样式处理需求
- 需要检查是否有其他功能依赖 CustomParagraph

### 方案2：修改 CustomParagraph 以兼容 TextAlign
如果必须保留 CustomParagraph，需要：
1. 确保与 TextAlign 扩展的属性处理兼容
2. 调整扩展加载顺序
3. 修改样式处理逻辑

## 📋 CustomParagraph 的原始用途

### 功能分析
1. **样式安全过滤**：过滤 `background` 和 `color` 相关样式
2. **HTML 解析增强**：支持解析特定的 `div` 标签
3. **自定义属性处理**：提供自定义的样式属性处理

### 是否必要？
- 如果项目不需要特殊的样式过滤，可以安全移除
- 如果需要样式安全，可以考虑其他实现方式
- 大多数情况下，TipTap 标准的 paragraph 节点已经足够

## ✅ 推荐操作

1. **立即修复**：注释掉 `CustomParagraph` 扩展
2. **功能测试**：验证 TextAlign 功能是否恢复正常
3. **影响评估**：检查其他功能是否受到影响
4. **长期方案**：如果需要样式过滤，考虑其他实现方式

## 🚨 注意事项

### 可能的副作用
1. **样式过滤失效**：不再过滤 `background` 和 `color` 样式
2. **HTML 解析变化**：不再特殊处理某些 `div` 标签
3. **现有内容兼容性**：需要检查现有内容是否正常显示

### 测试要点
1. TextAlign 功能是否正常工作
2. 现有文档内容是否正常显示
3. 样式应用是否符合预期
4. 其他编辑功能是否受影响

通过注释掉 `CustomParagraph` 扩展，我们解决了 TextAlign 功能失效的根本问题。
