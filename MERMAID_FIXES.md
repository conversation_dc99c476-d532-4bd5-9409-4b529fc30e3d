# MermaidNodeView.vue 问题修复报告

## 修复的问题

### 1. Button type 属性验证错误

**问题描述：**

- 控制台出现错误：`Invalid prop: custom validator check failed for prop "type"`
- 原因：Element Plus Button 组件对 type 属性有严格的验证规则

**解决方案：**

- 添加了 `computed` 导入
- 创建了计算属性 `previewButtonType` 和 `editButtonType` 来确保 type 值的正确性
- 替换了模板中的三元运算符，使用计算属性

```javascript
// 计算属性确保按钮 type 的正确性
const previewButtonType = computed(() => {
  return !isEditing.value ? 'primary' : 'default';
});

const editButtonType = computed(() => {
  return isEditing.value ? 'primary' : 'default';
});
```

### 2. Resize Handle 拖拽限制问题

**问题描述：**

- 在点击编辑按钮后，resize-handle 只能缩小和拉伸到一定宽度，无法继续拉大
- 原因：`handleResize` 函数中没有应用最大值限制，且频繁的属性更新可能导致性能问题

**解决方案：**

#### 2.1 移除尺寸限制，允许自由拖拽

```javascript
// 定义最小尺寸，防止组件过小无法使用
const MIN_SIZE = 50;
```

#### 2.2 修复 handleResize 函数

- 移除了最大值限制，允许无限拖拽
- 只保留最小值防止组件过小无法使用
- 移除了调试日志

```javascript
function handleResize(e) {
  if (!isResizing.value) return;

  const deltaX = e.clientX - startX.value;
  const deltaY = e.clientY - startY.value;

  // 允许自由拖拽，只设置最小值防止尺寸过小无法使用
  const newWidth = Math.max(MIN_SIZE, startPosX.value + deltaX);
  const newHeight = Math.max(MIN_SIZE, startPosY.value + deltaY);

  width.value = newWidth;
  height.value = newHeight;
  // 在拖拽过程中不更新属性，只在拖拽结束时更新
}
```

#### 2.3 优化性能

- 添加了防抖处理，避免过于频繁的属性更新
- 在拖拽过程中不更新 TipTap 属性，只在拖拽结束时更新

```javascript
// 防抖处理函数
let sizeChangeTimeout = null;

function handleSizeChange() {
  // 清除之前的定时器
  if (sizeChangeTimeout) {
    clearTimeout(sizeChangeTimeout);
  }

  // 防抖处理，避免过于频繁的更新
  sizeChangeTimeout = setTimeout(() => {
    props.updateAttributes({ width: width.value, height: height.value });
    nextTick(renderMermaid);
  }, 16); // 约60fps的更新频率
}
```

#### 2.4 更新 el-input-number 限制

- 移除最大值限制，允许用户输入任意大小
- 只保留最小值限制防止组件过小

#### 2.5 移除 SVG 尺寸限制

- 允许 SVG 根据容器大小自由缩放

#### 2.6 内存泄漏防护

- 在组件卸载时清理定时器
- 确保事件监听器的正确移除

```javascript
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);

  // 清理定时器
  if (sizeChangeTimeout) {
    clearTimeout(sizeChangeTimeout);
  }
});
```

## 修复效果

1. **Button type 错误已解决**：不再出现 type 属性验证错误
2. **Resize 功能完全自由**：可以无限拖拽调整大小，没有最大值限制
3. **性能优化**：减少了不必要的属性更新和重渲染
4. **代码健壮性**：添加了内存泄漏防护和错误处理
5. **SVG 显示正常**：移除了尺寸限制，SVG 可以根据容器自由缩放

## 测试建议

1. 测试编辑模式下的 resize 功能
2. 测试预览模式下的 resize 功能
3. 验证尺寸限制是否正确应用
4. 检查控制台是否还有 type 属性错误
5. 测试快速拖拽时的性能表现
