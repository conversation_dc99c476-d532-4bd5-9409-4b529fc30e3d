# TipTap 编辑器样式设置失效问题 - 解决方案

## 问题分析

### 🔍 根本原因发现

通过用户反馈发现：**清空内容后重新输入，样式操作就正常了**。这表明问题不在内容同步机制，而在于：

### 核心问题

1. **远程内容结构不兼容**：远程加载的 HTML 结构不符合 TipTap TextAlign 扩展的要求
2. **节点类型不匹配**：远程内容可能使用 `<div>` 而不是 `<p>` 标签
3. **扩展无法识别**：TextAlign 扩展只能在特定节点类型上工作（paragraph, heading, mermaid）
4. **HTML 解析问题**：TipTap 无法正确解析远程 HTML 为支持的节点类型

## 解决方案

### 🎯 核心解决方案：内容预处理

添加 `preprocessContent` 方法，在设置内容前将远程 HTML 转换为 TipTap 兼容的结构：

```javascript
// 预处理远程内容，确保兼容 TipTap 扩展
preprocessContent(content) {
  if (!content || typeof content !== 'string') {
    return content;
  }

  // 创建临时 DOM 来处理 HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = content;

  // 将 div 标签转换为 p 标签，确保 TextAlign 扩展能够工作
  const divs = tempDiv.querySelectorAll('div');
  divs.forEach(div => {
    // 只转换包含文本内容的 div，避免破坏复杂结构
    if (div.children.length === 0 || (div.children.length === 1 && div.children[0].tagName === 'BR')) {
      const p = document.createElement('p');
      p.innerHTML = div.innerHTML || '<br>';
      // 保留原有的样式属性
      if (div.style.cssText) {
        p.style.cssText = div.style.cssText;
      }
      div.parentNode.replaceChild(p, div);
    }
  });

  // 确保空内容有正确的结构
  if (tempDiv.innerHTML.trim() === '' || tempDiv.innerHTML === '<br>') {
    return '<p><br></p>';
  }

  // 确保根级文本节点被包装在 p 标签中
  const childNodes = Array.from(tempDiv.childNodes);
  childNodes.forEach(node => {
    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
      const p = document.createElement('p');
      p.textContent = node.textContent;
      tempDiv.replaceChild(p, node);
    }
  });

  return tempDiv.innerHTML;
}
```

### 1. 改进 modelValue 监听机制

```javascript
// 修改前（问题代码）
modelValue: {
  handler(newContent) {
    if (this.editor && !this.isUpdatingFromEditor) {
      this.editor.commands.setContent(newContent);
    }
  },
  immediate: true
}

// 修改后（解决方案）
modelValue: {
  handler(newContent) {
    if (this.editor && !this.isUpdatingFromEditor) {
      // 添加内容比较，避免不必要的更新
      const currentContent = this.editor.getHTML();
      if (currentContent !== newContent) {
        // 保存当前编辑状态
        const { from, to } = this.editor.state.selection;

        // 更新内容
        this.editor.commands.setContent(newContent);

        // 恢复光标位置（如果可能）
        this.$nextTick(() => {
          try {
            this.editor.commands.setTextSelection({ from, to });
          } catch (e) {
            // 如果无法恢复选择，至少设置焦点
            this.editor.commands.focus();
          }
        });
      }
    }
  },
  immediate: false // 改为 false，避免初始化冲突
}
```

### 2. 改进标志位控制机制

```javascript
// 在 data 中添加更精确的控制
data() {
  return {
    isUpdatingFromEditor: false,
    updateTimeout: null, // 添加防抖控制
    // ... 其他数据
  }
}

// 改进 onUpdate 回调
onUpdate: ({ editor }) => {
  // 清除之前的超时
  if (this.updateTimeout) {
    clearTimeout(this.updateTimeout);
  }

  var html = editor.getHTML();
  html = html.replace(/ {2,}/g, (match) => {
    return '&nbsp;'.repeat(match.length);
  });

  this.isUpdatingFromEditor = true;
  this.$emit('update:modelValue', html);

  // 使用防抖重置标志位
  this.updateTimeout = setTimeout(() => {
    this.isUpdatingFromEditor = false;
  }, 100); // 100ms 防抖
}
```

### 3. 添加编辑器就绪检查

```javascript
// 在 mounted 中改进初始化
mounted() {
  this.initEditor();

  // 等待编辑器完全初始化后再设置初始内容
  this.$nextTick(() => {
    if (this.modelValue && this.editor) {
      this.editor.commands.setContent(this.modelValue);
    }
  });
}
```

### 4. 添加内容比较工具方法

```javascript
methods: {
  // 添加内容比较方法
  isContentDifferent(newContent) {
    if (!this.editor) return true;

    const currentContent = this.editor.getHTML();
    // 标准化内容进行比较（移除空白差异）
    const normalize = (content) => content.replace(/\s+/g, ' ').trim();

    return normalize(currentContent) !== normalize(newContent);
  },

  // 安全的内容更新方法
  safeSetContent(content) {
    if (!this.editor || !this.isContentDifferent(content)) {
      return;
    }

    // 保存编辑状态
    const { from, to } = this.editor.state.selection;
    const hasFocus = this.editor.isFocused;

    // 更新内容
    this.editor.commands.setContent(content);

    // 恢复编辑状态
    this.$nextTick(() => {
      try {
        if (hasFocus) {
          this.editor.commands.focus();
          // 尝试恢复选择
          if (from !== to) {
            this.editor.commands.setTextSelection({ from, to });
          }
        }
      } catch (e) {
        console.warn('Failed to restore editor state:', e);
      }
    });
  }
}
```

## 实施步骤

1. **第一步**：修改 `modelValue` 的 watch 配置，添加内容比较
2. **第二步**：改进 `onUpdate` 回调的标志位控制
3. **第三步**：优化编辑器初始化时机
4. **第四步**：添加编辑状态保存和恢复机制
5. **第五步**：测试各种场景，确保样式操作不被覆盖

## 预期效果

- ✅ 用户样式操作不会被意外覆盖
- ✅ 减少不必要的内容更新操作
- ✅ 保持良好的编辑体验（光标位置、选择状态）
- ✅ 避免初始化时的冲突问题
- ✅ 提升编辑器性能和稳定性
