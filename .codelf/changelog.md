## 2025-01-15 14:30:00

### 1. 修复 MermaidNodeView 组件的 Button type 验证错误和 resize 限制问题

**Change Type**: fix

> **Purpose**: 解决 TipTap Mermaid 扩展中的两个关键问题：Element Plus Button 的 type 属性验证错误和 resize-handle 拖拽限制
> **Detailed Description**:
>
> 1. 修复了 Element Plus Button 组件的 type 属性验证错误，通过添加计算属性确保类型安全
> 2. 移除了 resize-handle 的最大值限制，允许用户无限拖拽调整 Mermaid 图表大小
> 3. 优化了性能，添加防抖处理避免频繁的属性更新
> 4. 移除了 SVG 的尺寸限制，允许图表自由缩放
>    **Reason for Change**: 用户反馈控制台出现 type 验证错误，且 resize 功能受限，影响用户体验
>    **Impact Scope**: 仅影响 TipTap Mermaid 扩展组件，不影响其他模块
>    **API Changes**: 无 API 变更
>    **Configuration Changes**: 无配置变更
>    **Performance Impact**: 通过防抖处理提升了拖拽性能，减少了不必要的重渲染

```
src
- components
  - TipTapRichTextEditor
    - extensions
      - Mermaid
        - MermaidNodeView.vue    // fix: 修复 Button type 验证和 resize 限制问题
- MERMAID_FIXES.md               // add: 添加详细的修复报告文档
```

## 2025-01-15 16:45:00

### 2. 为 MermaidNodeView 添加文本对齐功能

**Change Type**: feature

> **Purpose**: 为 TipTap Mermaid 扩展添加文本对齐功能，支持左对齐、居中、右对齐和两端对齐
> **Detailed Description**:
>
> 1. 让 `mermaid-block` 外层容器的宽度跟随内部容器的 resize 宽度同步
> 2. 使用 width + margin 配合的方式实现对齐，而不是 text-align
> 3. 设置 `display: block` 确保 margin 对齐能够生效
> 4. 集成 TipTap 的 TextAlign 扩展，支持工具栏按钮操作
> 5. 支持四种对齐方式：左对齐(margin-left:0)、居中(margin:auto)、右对齐(margin-right:0)、两端对齐(width:100%)
>    **Reason for Change**: 用户需要能够对 Mermaid 图表进行对齐操作，提升编辑体验
>    **Impact Scope**: 仅影响 TipTap Mermaid 扩展组件，与现有功能完全兼容
>    **API Changes**:
>
> - 添加了 `textAlign` 属性到 Mermaid 节点定义
> - TipTap TextAlign 扩展新增支持 'mermaid' 类型
>   **Configuration Changes**: 无配置变更
>   **Performance Impact**: 添加了响应式计算属性，性能影响微乎其微

```
src
- components
  - TipTapRichTextEditor
    - extensions
      - Mermaid
        - MermaidNodeView.js     // add: 添加 textAlign 属性定义和解析逻辑
        - MermaidNodeView.vue    // add: 添加 blockStyle 计算属性，实现 width+margin 对齐
    - index.vue                  // modify: TextAlign 配置添加 'mermaid' 类型支持
- MERMAID_ALIGNMENT_IMPLEMENTATION.md  // add: 添加文本对齐功能实现文档
```

## 2025-01-15 18:30:00

### 3. 修复 TipTap TextAlign 功能失效问题并为图片添加对齐支持

**Change Type**: fix + feature

> **Purpose**: 修复 TextAlign 扩展功能失效问题，并为 ImageWithNodeView 添加文本对齐功能
> **Detailed Description**:
>
> 1. **问题诊断**：发现 CustomParagraph 扩展与 TextAlign 扩展冲突，导致文字对齐功能失效
> 2. **根本修复**：注释掉 CustomParagraph 扩展，恢复 TipTap 标准 paragraph 节点
> 3. **图片对齐功能**：参考 MermaidNodeView 实现，为图片添加 width + margin 对齐支持
> 4. **统一对齐方案**：所有节点类型（文字、图片、Mermaid）使用相同的对齐实现逻辑
> 5. **扩展 TextAlign 配置**：添加对 'image' 节点类型的支持
>    **Reason for Change**: 用户反馈文字对齐功能在远程内容加载后失效，需要清空重新输入才能使用
>    **Impact Scope**: 影响 TipTap 编辑器的对齐功能，修复后所有节点类型都支持对齐操作
>    **API Changes**:
>
> - 移除了 CustomParagraph 扩展（可能影响样式过滤功能）
> - ImageWithNodeView 新增 textAlign 属性支持
> - TextAlign 扩展配置新增 'image' 类型
>   **Configuration Changes**: 注释掉 CustomParagraph 扩展，修改 TextAlign 配置
>   **Performance Impact**: 移除了自定义段落处理，可能略微提升性能

```
src
- components
  - TipTapRichTextEditor
    - extensions
      - Image
        - ImageWithNodeView.js   // add: 添加 textAlign 属性定义和解析逻辑
        - ImageNodeView.vue      // add: 添加 wrapperStyle 计算属性，实现图片对齐
    - index.vue                  // fix: 注释 CustomParagraph 扩展，修复 TextAlign 冲突
                                 // add: TextAlign 配置添加 'image' 类型支持
- TIPTAP_CUSTOMPARAGRAPH_ANALYSIS.md     // add: CustomParagraph 冲突分析文档
- TIPTAP_ALIGNMENT_COMPLETE_SOLUTION.md  // add: 完整解决方案文档
```
