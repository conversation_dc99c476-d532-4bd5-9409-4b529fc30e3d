# Mermaid 节点文本对齐功能 - 正确实现方案

## 核心思路

让 `mermaid-block` 外层容器的宽度跟随内部容器的 resize 宽度同步，然后使用 `margin` 来实现对齐。关键是 width + margin 的配合。

## 关键问题分析

### 之前的问题

- `mermaid-block` 的宽度是 `auto`，会根据内容自动调整
- 在 `inline-block` 元素上设置 margin 对齐无效
- 直接使用 `text-align` 在宽度为 `auto` 的容器上效果不明显

### 解决方案

- **关键**：让 `mermaid-block` 的宽度明确设置为内部容器的宽度
- 将 `mermaid-block` 设置为 `display: block`
- 使用 `margin` 来实现对齐：left/center/right/justify
- 当用户拖拽调整大小时，外层容器宽度会同步更新

## 实现细节

### 1. 修改 MermaidNodeView.js

添加 `textAlign` 属性支持：

```javascript
addAttributes() {
  return {
    code: { default: '' },
    width: { default: 600 },
    height: { default: 100 },
    textAlign: {
      default: null,
      parseHTML: element => {
        const alignment = element.style.textAlign
        return ['left', 'center', 'right', 'justify'].includes(alignment) ? alignment : null
      },
      renderHTML: attributes => {
        if (!attributes.textAlign) {
          return {}
        }
        return { style: `text-align: ${attributes.textAlign}` }
      },
    },
  };
},
```

### 2. 修改 MermaidNodeView.vue

#### 添加样式绑定

```vue
<node-view-wrapper
  class="mermaid-block"
  :style="blockStyle"
  @mouseenter="hovered = true"
  @mouseleave="hovered = false"
></node-view-wrapper>
```

#### 添加响应式变量

```javascript
const textAlign = ref(props.node.attrs.textAlign || null);
```

#### 关键计算属性

```javascript
// 计算外层容器样式，让宽度跟随内部容器，并使用 margin 实现对齐
const blockStyle = computed(() => {
  const style = {
    width: width.value + 'px' // 关键：让外层容器宽度跟随内部容器
    display: 'block' // 确保是块级元素，这样 margin 对齐才能生效
  };

  // 使用 margin 实现对齐
  if (textAlign.value) {
    switch (textAlign.value) {
      case 'left':
        style.marginLeft = '0';
        style.marginRight = 'auto';
        break;
      case 'center':
        style.marginLeft = 'auto';
        style.marginRight = 'auto';
        break;
      case 'right':
        style.marginLeft = 'auto';
        style.marginRight = '0';
        break;
      case 'justify':
        style.width = '100%';
        style.marginLeft = '0';
        style.marginRight = '0';
        break;
    }
  }

  return style;
});
```

#### 添加属性监听

```javascript
watch(
  () => props.node.attrs.textAlign,
  (val) => {
    textAlign.value = val;
  }
);
```

### 3. 配置 TipTap 编辑器

```javascript
TextAlign.configure({
  types: ['heading', 'paragraph', 'mermaid']
}),
```

## 工作原理

1. **宽度同步**：`mermaid-block` 的宽度始终等于内部容器的宽度
2. **块级元素**：设置 `display: block` 确保 margin 对齐能够生效
3. **margin 对齐**：通过设置不同的 margin 值实现左对齐、居中、右对齐
4. **动态更新**：当用户拖拽调整大小时，外层容器宽度会实时更新
5. **TipTap 集成**：通过 TextAlign 扩展，用户可以使用工具栏按钮进行对齐

## 使用效果

- **左对齐**：Mermaid 图表在编辑器中左对齐显示
- **居中**：Mermaid 图表在编辑器中居中显示
- **右对齐**：Mermaid 图表在编辑器中右对齐显示
- **两端对齐**：Mermaid 图表占满可用宽度

## 技术优势

1. **简单可靠**：利用标准的 CSS `margin` 属性实现对齐
2. **完全兼容**：不影响现有的拖拽、编辑等功能
3. **性能良好**：只是简单的样式计算，无额外开销
4. **用户友好**：通过工具栏按钮即可操作，符合用户习惯
5. **精确控制**：width + margin 的配合提供了精确的对齐控制

这个方案通过 width 和 margin 的配合，完美解决了块级元素的对齐问题。
