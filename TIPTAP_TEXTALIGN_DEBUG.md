# TipTap TextAlign 扩展调试指南

## 🔍 问题现象
- **不工作**：文字对齐操作（TextAlign 扩展）
- **正常工作**：字体、粗体、插入表格等其他操作

## 🎯 分析思路

### 关键差异
**工作正常的扩展**：
- Color, Highlight, FontFamily, FontSize: `types: ['textStyle']`
- 这些扩展作用于 textStyle 节点，可以应用到任何文本上

**可能有问题的扩展**：
- TextAlign: `types: ['heading', 'paragraph', 'mermaid']`
- 这个扩展只能作用于特定的块级节点

## 🛠️ 调试步骤

### 1. 使用调试按钮
在编辑器工具栏中添加了红色的"🐛 调试"按钮：

**操作步骤**：
1. 加载远程内容到编辑器
2. 选中一段文字
3. 点击"🐛 调试"按钮
4. 查看控制台输出

**关键信息**：
- 节点类型是什么？
- TextAlign 是否可用？
- 当前活跃的节点类型有哪些？

### 2. 检查控制台输出
调试信息包括：
```
=== 节点类型调试信息 ===
选择范围: {from: 1, to: 10}
节点类型: paragraph {pos: 0, content: "测试文字", attrs: {}, marks: []}
活跃节点类型: ["paragraph"]
TextAlign 可用性: {
  canSetLeft: true,
  canSetCenter: true,
  isLeftActive: false,
  isCenterActive: false
}
```

## 🔧 尝试的修复方案

### 方案1：扩展 TextAlign 支持的节点类型
```javascript
TextAlign.configure({
  types: ['heading', 'paragraph', 'mermaid', 'div', 'blockquote', 'listItem']
}),
```

**理由**：远程内容可能被解析为其他节点类型（如 div），需要扩展支持范围。

### 方案2：检查节点解析问题
如果调试显示节点类型不是 paragraph，可能需要：
1. 检查远程内容的 HTML 结构
2. 确认 TipTap 如何解析这些内容
3. 可能需要内容转换（但要避免破坏其他插件）

## 📋 测试场景

### 测试1：基础调试
1. 加载远程内容
2. 选中文字，点击调试按钮
3. 记录节点类型和 TextAlign 可用性

### 测试2：对比测试
1. 清空编辑器，手动输入相同文字
2. 选中文字，点击调试按钮
3. 对比两种情况的节点类型差异

### 测试3：功能验证
1. 在调试信息显示 `canSetCenter: true` 的情况下
2. 尝试点击居中按钮
3. 检查是否生效

## 🎯 预期结果

### 如果修复成功
- 调试信息显示 `canSetCenter: true`
- 点击对齐按钮后样式生效
- 控制台无错误信息

### 如果仍有问题
- 记录具体的节点类型
- 检查是否需要添加更多节点类型到 TextAlign 配置
- 或者需要其他解决方案

## 🚨 注意事项

1. **不要破坏其他插件**：避免过度的内容预处理
2. **保持兼容性**：确保修改不影响现有功能
3. **性能考虑**：调试代码仅用于问题诊断，解决后应移除

## 📊 可能的问题类型

### 类型1：节点类型不匹配
- **现象**：调试显示节点类型不在 TextAlign 支持列表中
- **解决**：添加对应节点类型到 types 配置

### 类型2：扩展初始化问题
- **现象**：节点类型正确但 `canSetCenter: false`
- **解决**：检查扩展初始化和配置

### 类型3：内容结构问题
- **现象**：远程内容结构特殊，无法正确解析
- **解决**：可能需要特殊处理（但要小心）

通过这个调试流程，我们应该能够准确定位问题所在！
